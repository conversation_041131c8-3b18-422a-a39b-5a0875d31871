import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Combines class names with Tailwind CSS classes
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Formats a date string into a human-readable format
 * @param dateString - ISO date string
 * @param locale - Locale for formatting (default: 'en-US')
 * @returns Formatted date string
 */
export function formatDate(dateString: string, locale: string = 'en-US'): string {
  const date = new Date(dateString)

  // Format options for different locales
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }

  // Return formatted date
  return date.toLocaleDateString(locale, options)
}

/**
 * Truncates text to a specified length and adds ellipsis
 * @param text - Text to truncate
 * @param maxLength - Maximum length before truncation
 * @returns Truncated text with ellipsis if needed
 */
export function truncateText(text: string, maxLength: number = 150): string {
  if (text.length <= maxLength) return text

  // Find the last space before maxLength to avoid cutting words
  const lastSpace = text.lastIndexOf(' ', maxLength)
  const truncated = text.substring(0, lastSpace > 0 ? lastSpace : maxLength)

  return `${truncated}...`
}

/**
 * Generates a slug from a string
 * @param text - Text to convert to slug
 * @returns URL-friendly slug
 */
export function slugify(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-')     // Replace spaces with hyphens
    .replace(/-+/g, '-')      // Remove consecutive hyphens
    .trim()                   // Trim whitespace
}

/**
 * Extracts reading time from HTML content
 * @param content - HTML content
 * @param wordsPerMinute - Reading speed (default: 200 words per minute)
 * @returns Estimated reading time in minutes
 */
export function getReadingTime(content: string, wordsPerMinute: number = 200): number {
  // Remove HTML tags and count words
  const text = content.replace(/<[^>]*>/g, '')
  const wordCount = text.trim().split(/\s+/).length

  // Calculate reading time and ensure minimum of 1 minute
  const readingTime = Math.ceil(wordCount / wordsPerMinute)
  return readingTime < 1 ? 1 : readingTime
}