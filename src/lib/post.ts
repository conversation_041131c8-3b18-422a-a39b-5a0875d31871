import fs from "fs"
import path from "path"
import matter from "gray-matter"
import { marked } from "marked"
import { markedHighlight } from "marked-highlight"
import hljs from "highlight.js"

// Configure marked with syntax highlighting and SEO-optimized options
marked.use(
    markedHighlight({
        langPrefix: 'language-',
        highlight(code, lang) {
            if (lang && hljs.getLanguage(lang)) {
                try {
                    return hljs.highlight(code, { language: lang }).value;
                } catch (err) {
                    console.error('Highlight.js error:', err);
                    return code;
                }
            }
            return hljs.highlightAuto(code).value;
        }
    }),
    {
        gfm: true,          // GitHub Flavored Markdown for advanced features
        breaks: true,       // Treat line breaks as <br> for better readability
    }
);

const postsDirectory = path.join(process.cwd(), "src/posts")

export interface Post {
    slug: string
    title: string
    date: string
    description: string
    content: string
    categories?: string[]
    image?: string
    author?: string
    language?: "en" | "ne"
    englishVersion?: string
    nepaliVersion?: string
    keywords?: string[]
    featured?: boolean
    readingTime?: number
}

export function getAllPosts(): Post[] {
    const fileNames = fs
        .readdirSync(postsDirectory)
        .filter((fileName) => fs.statSync(path.join(postsDirectory, fileName)).isFile() && fileName.endsWith(".md"))

    console.log("Found markdown files:", fileNames)

    const allPosts = fileNames.map((fileName) => {
        const slug = fileName.replace(/\.md$/, "")
        const fullPath = path.join(postsDirectory, fileName)
        try {
            const fileContents = fs.readFileSync(fullPath, "utf8")
            const { data, content } = matter(fileContents)

            // Default categories if none provided
            const categories = data.categories || []

            // Calculate reading time (approximately 200 words per minute)
            const wordCount = content.split(/\s+/).length
            const readingTime = Math.ceil(wordCount / 200)

            // Process categories to ensure consistency
            const processedCategories = Array.isArray(categories)
                ? categories.map(cat => typeof cat === 'string' ? cat.trim() : cat.toString().trim())
                : [typeof categories === 'string' ? categories.trim() : categories.toString().trim()];

            return {
                slug,
                title: data.title || "Untitled Post",
                date: data.date || new Date().toISOString().split("T")[0],
                description: data.description || "No description available",
                content: marked.parse(content || "") as string,
                categories: processedCategories,
                image: data.image || null,
                author: data.author || "Darshan Bajgain",
                language: data.language || "en",
                englishVersion: data.englishVersion || null,
                nepaliVersion: data.nepaliVersion || null,
                keywords: data.keywords || [],
                featured: data.featured || false,
                readingTime: readingTime || 1,
            }
        } catch (error) {
            console.error(`Error processing file ${fileName}:`, error)
            return {
                slug,
                title: "Error Loading Post",
                date: new Date().toISOString().split("T")[0],
                description: "Could not load this post due to an error",
                content: "<p>Error loading content</p>",
                categories: [],
                language: "en" as const,
                keywords: [],
                featured: false,
                readingTime: 1,
            }
        }
    })

    return allPosts.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
}

export function getPostBySlug(slug: string): Post {
    const fullPath = path.join(postsDirectory, `${slug}.md`)
    try {
        const fileContents = fs.readFileSync(fullPath, "utf8")
        const { data, content } = matter(fileContents)

        // Default categories if none provided
        const categories = data.categories || []

        // Calculate reading time (approximately 200 words per minute)
        const wordCount = content.split(/\s+/).length
        const readingTime = Math.ceil(wordCount / 200)

        // Process categories to ensure consistency
        const processedCategories = Array.isArray(categories)
            ? categories.map(cat => typeof cat === 'string' ? cat.trim() : cat.toString().trim())
            : [typeof categories === 'string' ? categories.trim() : categories.toString().trim()];

        return {
            slug,
            title: data.title || "Untitled Post",
            date: data.date || new Date().toISOString().split("T")[0],
            description: data.description || "No description available",
            content: marked.parse(content || "") as string,
            categories: processedCategories,
            image: data.image || null,
            author: data.author || "Darshan Bajgain",
            language: data.language || "en",
            englishVersion: data.englishVersion || null,
            nepaliVersion: data.nepaliVersion || null,
            keywords: data.keywords || [],
            featured: data.featured || false,
            readingTime: readingTime || 1,
        }
    } catch (error) {
        console.error(`Error loading post with slug ${slug}:`, error)
        return {
            slug,
            title: "Error Loading Post",
            date: new Date().toISOString().split("T")[0],
            description: "Could not load this post due to an error",
            content: "<p>Error loading content</p>",
            categories: [],
            language: "en" as const,
            keywords: [],
            featured: false,
            readingTime: 1,
        }
    }
}