---
title: "CSS Inline Code Styling Test"
date: "2024-01-15"
description: "Testing the improved inline code styling for CSS properties and functions in both light and dark modes."
categories: ["CSS", "Testing"]
---

# CSS Inline Code Styling Test

This post demonstrates the improved styling for inline code elements, particularly CSS properties and functions like `@property` and `color-mix()`.

## CSS Properties and Functions

Here are some examples of CSS-related inline code that should have better contrast in dark mode:

- The `@property` at-rule allows you to define custom CSS properties
- The `color-mix()` function enables color blending in CSS
- Properties like `background-color` and `border-radius` are fundamental
- Functions such as `calc()`, `var()`, and `clamp()` are very useful
- Modern CSS features include `@container` queries and `@layer` rules

## Regular Inline Code

Regular inline code like `const variable = "value"` and `function myFunc()` should also have improved contrast.

## Code in Different Contexts

When discussing CSS, we often mention properties like `display: flex`, `grid-template-columns`, and functions like `rgb()`, `hsl()`, and `oklch()`.

The new `@starting-style` rule and `transition-behavior` property are exciting additions to CSS.

## Testing Dark Mode

Switch to dark mode to see how these inline code elements now have better contrast and readability compared to before.
