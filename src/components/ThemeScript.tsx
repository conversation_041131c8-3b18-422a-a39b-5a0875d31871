'use client'

import { useEffect } from 'react'

// This component injects a script into the head to prevent flash of wrong theme
export function ThemeScript() {
  useEffect(() => {
    // This script runs on the client side only
    const setInitialTheme = () => {
      // Set light theme as default
      document.documentElement.classList.remove('dark')
      localStorage.setItem('theme', 'light')
    }

    setInitialTheme()
  }, [])

  return null
}

// This component provides a script tag that runs before React hydration
export function ThemeScriptTag() {
  // This script runs immediately on page load, before any React code
  return (
    <script
      dangerouslySetInnerHTML={{
        __html: `
          (function() {
            // Set light theme as default
            document.documentElement.classList.remove('dark');
            localStorage.setItem('theme', 'light');
          })();
        `,
      }}
    />
  )
}
