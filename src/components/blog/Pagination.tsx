"use client"

import { But<PERSON> } from "@/components/ui/button"
import { useBlogStore } from "@/store/blog-store"

interface PaginationProps {
    totalPages: number
}

export default function Pagination({ totalPages }: PaginationProps) {
    const { currentPage, setCurrentPage } = useBlogStore()

    if (totalPages <= 1) {
        return null
    }

    return (
        <div className="flex justify-center items-center gap-2 pt-8">
            <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(currentPage - 1, 1))}
                disabled={currentPage === 1}
                className="border-borderColor text-textPrimary hover:bg-backgroundSecondary disabled:opacity-50"
            >
                Previous
            </Button>

            <div className="flex items-center gap-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                        className={`h-9 w-9 p-0 ${
                            currentPage === page
                                ? "bg-accent text-white pointer-events-none"
                                : "border-borderColor text-textPrimary hover:bg-backgroundSecondary"
                        }`}
                    >
                        {page}
                    </Button>
                ))}
            </div>

            <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.min(currentPage + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="border-borderColor text-textPrimary hover:bg-backgroundSecondary disabled:opacity-50"
            >
                Next
            </Button>
        </div>
    )
}
