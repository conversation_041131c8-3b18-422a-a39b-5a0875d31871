"use client"

import type { Post } from "@/lib/post"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import PostGrid from "./PostGrid"
import { ArrowRight } from "lucide-react"

interface LatestPostsProps {
    posts: Post[]
    totalPosts: number
}

export default function LatestPosts({ posts, totalPosts }: LatestPostsProps) {
    return (
        <section id="latest-posts">
            <div className="space-y-8">
                <div className="flex items-center justify-between">
                    <h2 className="text-2xl font-bold text-textPrimary">Recent Posts</h2>
                    <Link
                        href="/all-posts"
                        className="text-accent hover:text-accent-hover flex items-center text-sm font-medium transition-colors"
                    >
                        View all <ArrowRight className="ml-1 h-4 w-4" />
                    </Link>
                </div>

                <PostGrid posts={posts} showEmptyState={false} />

                {totalPosts > posts.length && (
                    <div className="flex justify-center pt-8">
                        <Button
                            variant="outline"
                            asChild
                            className="border-borderColor text-textPrimary hover:bg-backgroundSecondary"
                        >
                            <Link href="/all-posts">View all posts</Link>
                        </Button>
                    </div>
                )}
            </div>
        </section>
    )
}