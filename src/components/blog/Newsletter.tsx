"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { motion } from "framer-motion"
import { Mail, ArrowRight, Check } from "lucide-react" // Add Check icon
import { useState } from "react"
import { toast } from "sonner"

interface NewsletterProps {
    title?: string
    description?: string
    buttonText?: string
    disclaimer?: string
}

export default function Newsletter({
    title = "Stay Updated",
    description = "Subscribe to my newsletter to get notified when I publish new content.",
    buttonText = "Subscribe",
    disclaimer = "I respect your privacy. Unsubscribe at any time.",
}: NewsletterProps) {
    const [email, setEmail] = useState("")
    const [loading, setLoading] = useState(false)
    const [success, setSuccess] = useState(false)

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setLoading(true)

        try {
            const response = await fetch('/api/subscribe', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email }),
            })

            const data = await response.json()

            if (!response.ok) {
                throw new Error(data.error || 'Subscription failed')
            }

            toast.success(data.message)
            setEmail("")
            setSuccess(true)
            
            // Reset success message after 15 seconds
            setTimeout(() => setSuccess(false), 15000)
        } catch (error) {
            console.error('Newsletter error:', error)
            toast.error('Failed to subscribe. Please try again.')
        } finally {
            setLoading(false)
        }
    }

    return (
        <section className="border-t border-borderColor pt-16">
            <div className="text-center space-y-6">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    viewport={{ once: true }}
                    className="space-y-4"
                >
                    <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-backgroundSecondary text-textSecondary text-sm font-medium">
                        <Mail className="h-4 w-4" />
                        <span>Newsletter</span>
                    </div>

                    <h2 className="text-2xl font-bold text-textPrimary">
                        {success ? "Thanks for subscribing! 🎉" : title}
                    </h2>

                    <p className="text-textSecondary max-w-md mx-auto">
                        {success
                            ? "Please check your email to confirm your subscription."
                            : description
                        }
                    </p>

                    {!success && (
                        <form onSubmit={handleSubmit} className="space-y-4 max-w-sm mx-auto">
                            <div className="flex gap-2">
                                <Input
                                    type="email"
                                    placeholder="Your email address"
                                    className="border-borderColor bg-backgroundPrimary focus-visible:ring-accent"
                                    required
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    disabled={loading}
                                />
                                <Button
                                    type="submit"
                                    className="bg-accent hover:bg-accent-hover text-white px-6"
                                    disabled={loading}
                                >
                                    {loading ? "..." : "Subscribe"}
                                </Button>
                            </div>
                            <p className="text-xs text-textMuted">{disclaimer}</p>
                        </form>
                    )}

                    {success && (
                        <div className="flex items-center justify-center gap-2 text-green-600">
                            <Check className="h-5 w-5" />
                            <span>Successfully subscribed!</span>
                        </div>
                    )}
                </motion.div>
            </div>
        </section>
    )
}

