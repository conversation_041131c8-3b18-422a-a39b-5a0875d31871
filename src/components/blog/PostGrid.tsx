"use client"

import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Calendar, Clock, ArrowRight } from "lucide-react"
import type { Post } from "@/lib/post"
import { useBlogStore } from "@/store/blog-store"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"

interface PostGridProps {
    posts: Post[]
    showEmptyState?: boolean
}

export default function PostGrid({ posts, showEmptyState = true }: PostGridProps) {
    const router = useRouter()
    const { getReadingTime, resetFilters } = useBlogStore()

    if (posts.length === 0 && showEmptyState) {
        return (
            <div className="text-center py-16 border border-borderColor rounded-lg bg-backgroundSecondary/30">
                <div className="text-4xl mb-4">🔍</div>
                <h3 className="text-lg font-semibold text-textPrimary mb-2">No posts found</h3>
                <p className="text-textSecondary mb-6">Try adjusting your search or filter criteria</p>
                <Button
                    variant="outline"
                    onClick={resetFilters}
                    className="border-borderColor text-textPrimary hover:bg-backgroundSecondary"
                >
                    Clear filters
                </Button>
            </div>
        )
    }

    return (
        <div className="space-y-8">
            {posts.map((post, index) => (
                <motion.div
                    key={post.slug}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                    <Link href={`/posts/${post.slug}`} className="group block">
                        <article className="py-6 border-b border-borderColor last:border-b-0 transition-all duration-200 hover:bg-backgroundSecondary/50 -mx-6 px-6 rounded-lg">
                            <div className="space-y-3">
                                {/* Date and Reading Time */}
                                <div className="flex items-center gap-4 text-sm text-textMuted">
                                    <time dateTime={post.date}>
                                            year: "numeric",
                                            month: "short",
                                            day: "numeric",
                                        })}
                                    </time>
                                    <span>•</span>
                                    <span>{getReadingTime(post.content)} min read</span>
                                </div>

                                {/* Title */}
                                <h3 className="text-xl font-semibold text-textPrimary group-hover:text-accent transition-colors duration-200">
                                    {post.title}
                                </h3>

                                {/* Description */}
                                <p className="text-textSecondary leading-relaxed line-clamp-2">
                                    {post.description}
                                </p>

                                {/* Categories */}
                                {post.categories && post.categories.length > 0 && (
                                    <div className="flex flex-wrap gap-2">
                                        {post.categories.slice(0, 3).map((category) => (
                                            <span
                                                key={category}
                                                className="text-xs px-2 py-1 bg-backgroundSecondary text-textSecondary rounded-md border border-borderColor"
                                            >
                                                {category}
                                            </span>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </article>
                    </Link>
                </motion.div>
            ))}
        </div>
    )
}

