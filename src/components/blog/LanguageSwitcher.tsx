"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Globe } from "lucide-react";

interface LanguageSwitcherProps {
    currentLanguage: "en" | "ne";
    englishSlug?: string;
    nepaliSlug?: string;
}

export default function LanguageSwitcher({ 
    currentLanguage, 
    englishSlug, 
    nepaliSlug 
}: LanguageSwitcherProps) {
    const router = useRouter();

    // If we don't have both slugs, don't render the component
    if (!englishSlug && !nepaliSlug) {
        return null;
    }

    // If we're missing one of the slugs, we can't switch to that language
    if (currentLanguage === "en" && !nepaliSlug) {
        return null;
    }
    if (currentLanguage === "ne" && !englishSlug) {
        return null;
    }

    const handleLanguageSwitch = () => {
        if (currentLanguage === "en" && nepaliSlug) {
            router.push(`/posts/${nepaliSlug}`);
        } else if (currentLanguage === "ne" && englishSlug) {
            router.push(`/posts/${englishSlug}`);
        }
    };

    return (
        <Button 
            onClick={handleLanguageSwitch} 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-2 text-buttons hover:bg-buttons/10"
        >
            <Globe className="h-4 w-4" />
            {currentLanguage === "en" ? "नेपालीमा पढ्नुहोस्" : "Read in English"}
        </Button>
    );
}
