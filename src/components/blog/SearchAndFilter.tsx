"use client"

import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Search, X } from "lucide-react"
import { useBlogStore } from "@/store/blog-store"

interface SearchAndFilterProps {
    allCategories: string[]
    totalPosts: number
}

export default function SearchAndFilter({ totalPosts }: SearchAndFilterProps) {

    const { search, setSearch } = useBlogStore()


    return (
        <div className="space-y-6">
            <div className="relative max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-textMuted h-4 w-4" />
                <Input
                    placeholder="Search posts..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-10 border-borderColor bg-backgroundPrimary focus-visible:ring-accent"
                />
                {search && (
                    <Button
                        variant="ghost"
                        size="icon"
                        className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 text-textMuted hover:text-textPrimary"
                        onClick={() => setSearch("")}
                    >
                        <X className="h-4 w-4" />
                    </Button>
                )}
            </div>

            {/* Stats */}
            <div className="text-sm text-textMuted">
                {totalPosts} post{totalPosts !== 1 ? "s" : ""} found
            </div>
        </div>
    )
}