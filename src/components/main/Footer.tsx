"use client"

import { GithubIcon, TwitterIcon, LinkedinIcon, InstagramIcon } from "lucide-react"
import { But<PERSON> } from "../ui/button"

export default function Footer() {
    const currentYear = new Date().getFullYear()

    return (
        <footer className="border-t border-borderColor mt-16 pt-8">
            <div className="max-w-4xl mx-auto px-6">
                <div className="text-center space-y-4">
                    <p className="text-textSecondary text-sm">
                        © {currentYear} Darshan Bajgain. All rights reserved.
                    </p>
                    <div className="flex justify-center items-center gap-4">
                        <Button 
                            variant="ghost" 
                            size="icon" 
                            className="text-textSecondary hover:text-textPrimary hover:bg-backgroundSecondary h-8 w-8"
                            asChild
                        >
                            <a href="https://github.com/darshanbajgain" target="_blank" rel="noopener noreferrer" aria-label="GitHub">
                                <GithubIcon className="h-4 w-4" />
                            </a>
                        </Button>
                        <Button 
                            variant="ghost" 
                            size="icon" 
                            className="text-textSecondary hover:text-textPrimary hover:bg-backgroundSecondary h-8 w-8" 
                            asChild
                        >
                            <a href="https://x.com/iamthesun77" target="_blank" rel="noopener noreferrer" aria-label="X (Twitter)">
                                <TwitterIcon className="h-4 w-4" />
                            </a>
                        </Button>
                        <Button 
                            variant="ghost" 
                            size="icon" 
                            className="text-textSecondary hover:text-textPrimary hover:bg-backgroundSecondary h-8 w-8" 
                            asChild
                        >
                            <a href="https://www.linkedin.com/in/devdarshan10/" target="_blank" rel="noopener noreferrer" aria-label="LinkedIn">
                                <LinkedinIcon className="h-4 w-4" />
                            </a>
                        </Button>
                        <Button 
                            variant="ghost" 
                            size="icon" 
                            className="text-textSecondary hover:text-textPrimary hover:bg-backgroundSecondary h-8 w-8" 
                            asChild
                        >
                            <a href="https://www.instagram.com/iamdarshanbajgain/" target="_blank" rel="noopener noreferrer" aria-label="Instagram">
                                <InstagramIcon className="h-4 w-4" />
                            </a>
                        </Button>
                    </div>
                </div>
            </div>
        </footer>
    )
}
