'use client'

import { useEffect } from 'react'

/**
 * Component to enhance inline code elements with better styling for CSS properties and functions
 */
export default function EnhanceInlineCode() {
  useEffect(() => {
    const enhanceInlineCode = () => {
      // Find all inline code elements (not in code blocks)
      const inlineCodeElements = document.querySelectorAll('.prose code:not([class*="language-"])')
      
      inlineCodeElements.forEach((codeElement) => {
        // Skip if already enhanced
        if (codeElement.classList.contains('css-property') ||
            codeElement.classList.contains('css-function') ||
            codeElement.hasAttribute('data-enhanced')) {
          return
        }

        const text = codeElement.textContent || ''
        
        // CSS Properties - starts with @ or common CSS property patterns
        if (text.startsWith('@') ||
            text.match(/^(background|border|color|display|font|grid|flex|margin|padding|position|text|width|height|top|left|right|bottom|z-index|opacity|transform|transition|animation)-/) ||
            text.includes('property') ||
            text.includes('layer') ||
            text.includes('container') ||
            text.includes('starting-style') ||
            text.includes('supports') ||
            text.includes('media') ||
            text.includes('keyframes')) {
          codeElement.classList.add('css-property')
        }

        // CSS Functions - ends with () or contains function patterns
        else if (text.includes('()') ||
                 text.includes('color-mix') ||
                 text.includes('calc') ||
                 text.includes('var') ||
                 text.includes('clamp') ||
                 text.includes('min(') ||
                 text.includes('max(') ||
                 text.includes('rgb') ||
                 text.includes('hsl') ||
                 text.includes('oklch') ||
                 text.includes('linear-gradient') ||
                 text.includes('radial-gradient') ||
                 text.includes('conic-gradient') ||
                 text.includes('url(') ||
                 text.includes('attr(') ||
                 text.includes('counter(')) {
          codeElement.classList.add('css-function')
        }

        // Mark as enhanced to avoid reprocessing
        codeElement.setAttribute('data-enhanced', 'true')
      })
    }

    // Run enhancement after a short delay to ensure DOM is ready
    const timeoutId = setTimeout(enhanceInlineCode, 100)
    
    // Also run on any dynamic content changes
    const observer = new MutationObserver(enhanceInlineCode)
    observer.observe(document.body, { 
      childList: true, 
      subtree: true 
    })

    return () => {
      clearTimeout(timeoutId)
      observer.disconnect()
    }
  }, [])

  return null
}
