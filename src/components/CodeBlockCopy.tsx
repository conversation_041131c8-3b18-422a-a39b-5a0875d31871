'use client'

import { useEffect } from 'react'

export default function CodeBlockCopy() {
  useEffect(() => {
    // Function to enhance code blocks with language display and copy button
    const enhanceCodeBlocks = () => {
      const codeBlocks = document.querySelectorAll('pre > code[class*="language-"]')

      codeBlocks.forEach(codeBlock => {
        const pre = codeBlock.parentElement
        if (!pre || pre.parentElement?.classList.contains('code-block-content')) return

        // Get the language from the class
        const languageMatch = codeBlock.className.match(/language-(\w+)/)
        const language = languageMatch ? languageMatch[1] : 'plaintext'

        // Get the code text
        const codeText = codeBlock.textContent || ''

        // Create wrapper elements
        const wrapper = document.createElement('div')
        wrapper.className = 'code-block-wrapper'

        const header = document.createElement('div')
        header.className = 'code-block-header'

        const langSpan = document.createElement('span')
        langSpan.className = 'code-language'
        langSpan.textContent = language

        const copyButton = document.createElement('button')
        copyButton.className = 'copy-code-button'
        copyButton.setAttribute('aria-label', 'Copy code')
        copyButton.setAttribute('data-clipboard-text', codeText)

        const copyText = document.createElement('span')
        copyText.className = 'copy-text'
        copyText.textContent = 'Copy'

        copyButton.appendChild(copyText)
        header.appendChild(langSpan)
        header.appendChild(copyButton)

        const content = document.createElement('div')
        content.className = 'code-block-content'

        // Move the pre element into the content div
        if (pre.parentNode) {
          pre.parentNode.insertBefore(wrapper, pre)
          content.appendChild(pre)
          wrapper.appendChild(header)
          wrapper.appendChild(content)
        }

        // Add click event listener to the copy button
        copyButton.addEventListener('click', () => {
          navigator.clipboard.writeText(codeText)
            .then(() => {
              copyText.textContent = 'Copied!'
              setTimeout(() => {
                copyText.textContent = 'Copy'
              }, 2000)
            })
            .catch(err => {
              console.error('Failed to copy text: ', err)
            })
        })
      })
    }

    // Initial enhancement
    enhanceCodeBlocks()

    // Setup for dynamically added code blocks (e.g., after client-side navigation)
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.addedNodes.length) {
          enhanceCodeBlocks()
        }
      })
    })

    observer.observe(document.body, { childList: true, subtree: true })

    // Cleanup
    return () => {
      observer.disconnect()
    }
  }, [])

  return null
}
