import type { <PERSON><PERSON><PERSON> } from "next"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { MailIcon, GlobeIcon } from "lucide-react"
import { GithubIcon, TwitterIcon, LinkedinIcon, InstagramIcon } from "@/components/icons/SocialIcons"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export const metadata: Metadata = {
    title: "About Me | Darshan's Blog",
    description: "Learn more about <PERSON><PERSON>, BSc CSIT graduate and frontend web developer sharing tech insights and personal reflections",
    keywords: ["Darshan Bajgain", "frontend developer", "BSc CSIT", "ASCOL", "web development", "about me"],
    openGraph: {
        title: "About Me | Dar<PERSON>'s Blog",
        description: "Learn more about <PERSON><PERSON>, BSc CSIT graduate and frontend web developer sharing tech insights and personal reflections",
        url: "https://blog.darshanbajgain.com.np/about",
        type: "website",
        images: [
            {
                url: "/images/avatar.png",
                width: 1200,
                height: 630,
                alt: "About Darshan Bajgain"
            }
        ],
    },
    twitter: {
        card: "summary_large_image",
        title: "About Me | Dar<PERSON>'s Blog",
        description: "<PERSON>rn more about <PERSON>shan Bajgain, BSc CSIT graduate and frontend web developer sharing tech insights and personal reflections",
        images: ["/images/avatar.png"],
    },
}

export default function AboutPage() {
    return (
        <div className="container max-w-7xl px-6 md:px-12 mx-auto mb-16 py-5">
            <div className="space-y-5">
                {/* Header Section */}
                <div className="space-y-4">
                    <h1 className="text-3xl md:text-4xl font-bold tracking-tight">About Me</h1>
                    <div className="h-1 w-20 bg-textPrimary"></div>
                </div>



                {/* Main Content - 2 Column Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
                    {/* Left Column - Bio */}
                    <div className="space-y-6 prose dark:prose-invert max-w-none">
                        {/* Profile Section */}
                        <div className="flex flex-col sm:flex-row gap-8 items-start">

                            <div className="space-y-4">
                                <h2 className="text-2xl font-semibold">Darshan Bajgain</h2>
                                <p className="text-muted-foreground">Frontend Web Developer & BSc CSIT Graduate</p>


                                <div className="flex items-center gap-4 mt-3">
                                    <a href="https://github.com/darshanbajgain" target="_blank" rel="noopener noreferrer" aria-label="GitHub" className="text-buttons hover:text-buttons/80 transition-colors">
                                        <GithubIcon className="h-5 w-5" />
                                    </a>
                                    <a href="https://twitter.com/iamthesun77" target="_blank" rel="noopener noreferrer" aria-label="Twitter" className="text-buttons hover:text-buttons/80 transition-colors">
                                        <TwitterIcon className="h-5 w-5" />
                                    </a>
                                    <a href="https://www.linkedin.com/in/devdarshan10/" target="_blank" rel="noopener noreferrer" aria-label="LinkedIn" className="text-buttons hover:text-buttons/80 transition-colors">
                                        <LinkedinIcon className="h-5 w-5" />
                                    </a>
                                    <a href="https://darshanbajgain.com.np/" target="_blank" rel="noopener noreferrer" aria-label="Website" className="text-buttons hover:text-buttons/80 transition-colors">
                                        <GlobeIcon className="h-5 w-5" />
                                    </a>
                                    <a href="https://www.instagram.com/iamdarshanbajgain/" target="_blank" rel="noopener noreferrer" aria-label="Instagram" className="text-buttons hover:text-buttons/80 transition-colors">
                                        <InstagramIcon className="h-5 w-5" />
                                    </a>
                                </div>
                            </div>
                        </div>
                        <h3 className="text-2xl font-semibold">My Journey</h3>

                        <p>
                            Hi there! I&apos;m Darshan, a passionate frontend web developer with about 1 year of experience in the field,
                            including both internships and professional work. I graduated from Amrit Science Campus (ASCOL) with a
                            BSc CSIT degree, which provided me with a strong foundation in computer science and technology.
                        </p>

                        <p>
                            Since graduation, I&apos;ve been working as a frontend web developer, focusing on modern JavaScript
                            frameworks like React and Next.js. I stay current with the latest trends and tools in frontend development
                            and am constantly exploring new technologies to enhance my skills and knowledge.
                        </p>

                        <h3 className="text-2xl font-semibold">Why I Blog</h3>
                        <p>
                            I created this blog to share both technical insights and personal reflections. On the technical side,
                            I write about frontend development, sharing tutorials, tips, and solutions to common problems I encounter
                            in my work. These posts help me deepen my understanding while providing value to other developers.
                        </p>

                        <p>
                            Beyond tech, I also share personal content that offers insights into my experiences, thoughts, and
                            observations about life. I believe that combining technical knowledge with personal growth creates
                            a more holistic approach to both development and life.
                        </p>

                        <p>
                            My goal is to create content that not only helps people solve technical problems but also provides
                            perspectives that might inspire or help them in their personal and professional journeys. I&apos;m passionate
                            about continuous learning and sharing what I discover along the way.
                        </p>
                    </div>

                    {/* Right Column - Skills & Contact */}
                    <div className="space-y-10">
                        {/* Skills Section - Single Card */}
                        <div className="space-y-6">
                            <h3 className="text-2xl font-semibold">My Skills</h3>

                            <Card className="overflow-hidden bg-cardBackground">
                                <CardContent className="p-6">
                                    <div className="space-y-6">
                                        <div>
                                            <h4 className="font-medium mb-3">Languages</h4>
                                            <div className="flex flex-wrap gap-2">
                                                <Badge variant="secondary" className="text-xs cursor-pointer font-normal transition-colors duration-200 bg-buttons text-textSecondary border-none px-3 py-1 shadow-md hover:bg-buttons/80"
                                                >JavaScript</Badge>
                                                <Badge variant="secondary" className="text-xs cursor-pointer font-normal transition-colors duration-200 bg-buttons text-textSecondary border-none px-3 py-1 shadow-md hover:bg-buttons/80"
                                                >TypeScript</Badge>
                                                <Badge variant="secondary" className="text-xs cursor-pointer font-normal transition-colors duration-200 bg-buttons text-textSecondary border-none px-3 py-1 shadow-md hover:bg-buttons/80"
                                                >HTML</Badge>
                                                <Badge variant="secondary" className="text-xs cursor-pointer font-normal transition-colors duration-200 bg-buttons text-textSecondary border-none px-3 py-1 shadow-md hover:bg-buttons/80"
                                                >CSS</Badge>
                                            </div>
                                        </div>

                                        <div>
                                            <h4 className="font-medium mb-3">Frameworks</h4>
                                            <div className="flex flex-wrap gap-2">
                                                <Badge variant="secondary" className="text-xs cursor-pointer font-normal transition-colors duration-200 bg-buttons text-textSecondary border-none px-3 py-1 shadow-md hover:bg-buttons/80"
                                                >React</Badge>
                                                <Badge variant="secondary" className="text-xs cursor-pointer font-normal transition-colors duration-200 bg-buttons text-textSecondary border-none px-3 py-1 shadow-md hover:bg-buttons/80"
                                                >Next.js</Badge>

                                                <Badge variant="secondary" className="text-xs cursor-pointer font-normal transition-colors duration-200 bg-buttons text-textSecondary border-none px-3 py-1 shadow-md hover:bg-buttons/80"
                                                >Tailwind CSS</Badge>

                                            </div>
                                        </div>

                                        <div>
                                            <h4 className="font-medium mb-3">State Management</h4>
                                            <div className="flex flex-wrap gap-2">

                                                <Badge variant="secondary" className="text-xs cursor-pointer font-normal transition-colors duration-200 bg-buttons text-textSecondary border-none px-3 py-1 shadow-md hover:bg-buttons/80"
                                                >Zustand</Badge>
                                                <Badge variant="secondary" className="text-xs cursor-pointer font-normal transition-colors duration-200 bg-buttons text-textSecondary border-none px-3 py-1 shadow-md hover:bg-buttons/80"
                                                >Context API</Badge>

                                            </div>
                                        </div>


                                        <div>
                                            <h4 className="font-medium mb-3">Tools & Others</h4>
                                            <div className="flex flex-wrap gap-2">
                                                <Badge variant="secondary" className="text-xs cursor-pointer font-normal transition-colors duration-200 bg-buttons text-textSecondary border-none px-3 py-1 shadow-md hover:bg-buttons/80"
                                                >Git</Badge>
                                                <Badge variant="secondary" className="text-xs cursor-pointer font-normal transition-colors duration-200 bg-buttons text-textSecondary border-none px-3 py-1 shadow-md hover:bg-buttons/80"
                                                >Vite</Badge>
                                                <Badge variant="secondary" className="text-xs cursor-pointer font-normal transition-colors duration-200 bg-buttons text-textSecondary border-none px-3 py-1 shadow-md hover:bg-buttons/80"
                                                >npm</Badge>
                                                <Badge variant="secondary" className="text-xs cursor-pointer font-normal transition-colors duration-200 bg-buttons text-textSecondary border-none px-3 py-1 shadow-md hover:bg-buttons/80"
                                                >Vercel</Badge>
                                                <Badge variant="secondary" className="text-xs cursor-pointer font-normal transition-colors duration-200 bg-buttons text-textSecondary border-none px-3 py-1 shadow-md hover:bg-buttons/80"
                                                >Figma</Badge>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Get in Touch Section */}
                        <div className="space-y-6">
                            <h3 className="text-2xl font-semibold">Get in Touch</h3>

                            <Card className="bg-cardBackground">
                                <CardContent className="p-6">
                                    <p className="mb-4 text-textPrimary">
                                        I&apos;m always open to new opportunities, collaborations, or just chatting about web development.
                                        Feel free to reach out to me through any of the social links above or via email.
                                    </p>

                                    <div className="space-y-4">
                                        <div className="flex items-center gap-3">
                                            <MailIcon className="h-5 w-5 text-buttons" />
                                            <span className="text-textPrimary/70"><EMAIL></span>
                                        </div>

                                        <div className="flex justify-start">
                                            <Button asChild className="h-9 bg-buttons hover:bg-buttons/90 text-textSecondary">
                                                <Link href="/contact">Contact Me</Link>
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}