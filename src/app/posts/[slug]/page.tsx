import { getPostBySlug, getAllPosts } from "@/lib/post"
import type { Metadata } from "next"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, ChevronLeft, User } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { notFound } from "next/navigation"
import SocialShare from "@/components/blog/SocialShare"
import LanguageSwitcher from "@/components/blog/LanguageSwitcher"
import { Toaster } from "sonner"
import Script from "next/script"

interface PostPageProps {
    params: Promise<{
        slug: string
    }>
}

// Generate metadata for the page
export async function generateMetadata({ params }: PostPageProps): Promise<Metadata> {
    try {
        const { slug } = await params;
        const post = getPostBySlug(slug)
        const url = `https://blog.darshanbajgain.com.np/posts/${slug}`;

        return {
            title: `${post.title} | <PERSON><PERSON>'s Blog`,
            description: post.description,
            keywords: [...(post.categories || []), "blog", "<PERSON><PERSON> Bajgain", "frontend development"],
            authors: [{ name: "<PERSON><PERSON>gai<PERSON>", url: "https://darshanbajgain.com.np" }],
            openGraph: {
                title: `${post.title} | Darshan's Blog`,
                description: post.description,
                url,
                type: "article",
                publishedTime: post.date,
                authors: ["Darshan Bajgain"],
                images: [
                    {
                        url: post.image || "/images/avatar.png",
                        width: 1200,
                        height: 630,
                        alt: post.title
                    }
                ],
            },
            twitter: {
                card: "summary_large_image",
                title: `${post.title} | Darshan's Blog`,
                description: post.description,
                creator: "@iamthesun77",
                images: [post.image || "/images/avatar.png"],
            },
        }
    } catch {
        return {
            title: "Post Not Found | Darshan's Blog",
            description: "The requested post could not be found.",
            openGraph: {
                title: "Post Not Found | Darshan's Blog",
                description: "The requested post could not be found.",
            },
            twitter: {
                card: "summary",
                title: "Post Not Found | Darshan's Blog",
                description: "The requested post could not be found.",
            },
        }
    }
}

// Generate static paths
export function generateStaticParams() {
    const posts = getAllPosts()
    return posts.map((post) => ({
        slug: post.slug,
    }))
}

// Estimate reading time (1 min per 200 words)
function getReadingTime(content: string) {
    const wordCount = content
        .replace(/<[^>]*>/g, "")
        .trim()
        .split(/\s+/).length
    const readingTime = Math.ceil(wordCount / 200)
    return readingTime < 1 ? 1 : readingTime
}

export default async function PostPage({ params }: PostPageProps) {
    let post

    try {
        const { slug } = await params;
        post = getPostBySlug(slug)
    } catch {
        notFound()
    }

    const readingTime = getReadingTime(post.content)
    const publishDate = new Date(post.date).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
    })

    // Format date for structured data
    const isoDate = new Date(post.date).toISOString()
    const siteUrl = "https://blog.darshanbajgain.com.np"
    const postUrl = `${siteUrl}/posts/${post.slug}`

    // Create structured data for SEO
    const jsonLd = {
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "headline": post.title,
        "description": post.description,
        "image": post.image ? `${siteUrl}${post.image}` : `${siteUrl}/images/avatar.png`,
        "datePublished": isoDate,
        "dateModified": isoDate,
        "author": {
            "@type": "Person",
            "name": post.author || "Darshan Bajgain",
            "url": "https://darshanbajgain.com.np"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Darshan's Blog",
            "logo": {
                "@type": "ImageObject",
                "url": `${siteUrl}/images/avatar.png`
            }
        },
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": postUrl
        },
        "keywords": [...(post.categories || []), "blog", "frontend development"].join(", ")
    }

    return (
        <>
            {/* Add structured data for SEO */}
            <Script id="blog-post-schema" type="application/ld+json">
                {JSON.stringify(jsonLd)}
            </Script>
            <article className="space-y-8">
                <Button variant="ghost" asChild className="mb-6 -ml-3">
                    <Link href="/" className="flex items-center gap-1 text-textSecondary hover:text-textPrimary">
                        <ChevronLeft className="h-4 w-4" />
                        Back
                    </Link>
                </Button>

                <header className="space-y-6">
                    {/* Post categories */}
                    {post.categories && post.categories.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                            {post.categories.map((category) => (
                                <span
                                    key={category}
                                    className="text-xs px-2 py-1 bg-backgroundSecondary text-textSecondary rounded-md border border-borderColor"
                                >
                                    {category}
                                </span>
                            ))}
                        </div>
                    )}

                    {/* Post title */}
                    <h1 className="text-3xl md:text-4xl font-bold text-textPrimary leading-tight">{post.title}</h1>

                    {/* Post description */}
                    <p className="text-lg text-textSecondary leading-relaxed">{post.description}</p>

                    {/* Post meta */}
                    <div className="flex flex-wrap items-center gap-4 text-sm text-textMuted">
                        <time dateTime={post.date}>{publishDate}</time>
                        <span>•</span>
                        <span>{readingTime} min read</span>
                        <span>•</span>
                        <span>By Darshan Bajgain</span>
                    </div>
                </header>

                {/* Post content */}
                <div
                    className="prose dark:prose-invert max-w-none prose-headings:font-bold prose-h2:text-2xl prose-h3:text-xl prose-a:text-accent hover:prose-a:text-accent-hover prose-a:break-words prose-img:rounded-lg prose-img:mx-auto prose-code:text-textPrimary prose-ol:list-decimal prose-ul:list-disc prose-p:text-base md:prose-p:text-lg prose-headings:leading-tight prose-headings:text-textPrimary prose-p:text-textPrimary prose-li:text-textPrimary"
                    dangerouslySetInnerHTML={{ __html: post.content }}
                />

                {/* Post footer */}
                <footer className="border-t border-borderColor pt-8 space-y-4">
                    <h3 className="text-lg font-semibold text-textPrimary">Share this post</h3>
                    <SocialShare
                        title={post.title}
                        url={`https://blog.darshanbajgain.com.np/posts/${post.slug}`}
                    />
                    <Toaster position="bottom-right" richColors />
                </footer>


        </article>
        </>
    )
}