import PostList from "@/components/blog/PostList"
import { But<PERSON> } from "@/components/ui/button"
import { getAllPosts } from "@/lib/post"
import { ChevronLeft } from "lucide-react"
import type { Metada<PERSON> } from "next"
import Link from "next/link"

export const metadata: Metadata = {
    title: "All Posts | Dar<PERSON>'s Blog",
    description: "Browse all articles and tutorials on web development, JavaScript, React, and more",
}

export default function AllPostsPage() {
    const posts = getAllPosts()

    return (
        <div className="space-y-8">
            <header>
                <Button variant="ghost" asChild className="mb-6 -ml-3">
                    <Link
                        href="/"
                        className="flex items-center gap-1 text-textSecondary hover:text-textPrimary"
                    >
                        <ChevronLeft className="h-4 w-4" />
                        Back
                    </Link>
                </Button>
                <h1 className="text-3xl font-bold mb-3 text-textPrimary">All Posts</h1>
                <p className="text-textSecondary">
                    Browse all {posts.length} articles and tutorials on web development, JavaScript, React, and more.
                </p>
            </header>

            <PostList initialPosts={posts} showSearch={true} />
        </div>
    )
}

