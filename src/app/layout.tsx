import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google"
import "./globals.css"
import "./prism.css"
import { ThemeProvider } from "@/theme/theme-provider"
import Navbar from "@/components/main/Navbar"
import Footer from "@/components/main/Footer"
import SyntaxHighlighter from "@/components/SyntaxHighlighter"
import CodeBlockCopy from "@/components/CodeBlockCopy"
import EnhanceInlineCode from "@/components/EnhanceInlineCode"
// Theme script is now inline in the head

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
})

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
})

export const metadata: Metadata = {
  title: "Dar<PERSON>'s Blog",
  description: "A personal blog sharing frontend development insights and daily life experiences by <PERSON><PERSON>",
  keywords: ["frontend development", "web development", "React", "Next.js", "JavaScript", "TypeScript", "personal blog", "Darshan Bajgain"],
  authors: [{ name: "<PERSON><PERSON>gai<PERSON>", url: "https://darshanbajgain.com.np" }],
  creator: "Darshan Bajgain",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://blog.darshanbajgain.com.np",
    title: "Darshan's Blog",
    description: "A personal blog sharing frontend development insights and daily life experiences by Darshan Bajgain",
    siteName: "Darshan's Blog",
    images: [
      {
        url: "/images/avatar.png",
        width: 1200,
        height: 630,
        alt: "Darshan's Blog"
      }
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Darshan's Blog",
    description: "A personal blog sharing frontend development insights and daily life experiences by Darshan Bajgain",
    creator: "@iamthesun77",
    images: ["/images/avatar.png"],
  },
  icons: {
    icon: [
      { url: "/favicon_io/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon_io/favicon-32x32.png", sizes: "32x32", type: "image/png" },
      { url: "/favicon_io/favicon.ico" }
    ],
    apple: [
      { url: "/favicon_io/apple-touch-icon.png" }
    ],
    other: [
      {
        rel: "android-chrome-192x192",
        url: "/favicon_io/android-chrome-192x192.png",
      },
      {
        rel: "android-chrome-512x512",
        url: "/favicon_io/android-chrome-512x512.png",
      }
    ]
  },
  manifest: "/favicon_io/site.webmanifest"
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="color-scheme" content="light" />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                // Apply light mode immediately to prevent flash
                document.documentElement.classList.remove('dark');
                localStorage.setItem('theme', 'light');
              })();
            `,
          }}
        />
      </head>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col bg-backgroundPrimary`}>
        <ThemeProvider>
          <SyntaxHighlighter />
          <CodeBlockCopy />
          <EnhanceInlineCode />
          <Navbar />
          <main className="flex-grow">{children}</main>
          <Footer />
        </ThemeProvider>
      </body>
    </html>
  )
}

