import PostList from "@/components/blog/PostList";
import { getAllPosts, type Post } from "@/lib/post";
import type { Metadata } from "next";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { notFound } from "next/navigation";

interface CategoryPageProps {
    params: Promise<{
        category: string
    }>
}


export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
    const { category } = await params;
    const decodedCategory = decodeURIComponent(category);
    const url = `https://blog.darshanbajgain.com.np/categories/${category}`;

    return {
        title: `${decodedCategory} Posts | Dar<PERSON>'s Blog`,
        description: `Browse all articles and tutorials about ${decodedCategory} by <PERSON><PERSON>gain`,
        keywords: [decodedCategory, "blog", "Darshan Bajgain", "frontend development", "tutorials"],
        openGraph: {
            title: `${decodedCategory} Posts | <PERSON><PERSON>'s Blog`,
            description: `Browse all articles and tutorials about ${decodedCategory} by <PERSON><PERSON>`,
            url,
            type: "website",
            images: [
                {
                    url: "/images/avatar.png",
                    width: 1200,
                    height: 630,
                    alt: `${decodedCategory} - Darshan's Blog`
                }
            ],
        },
        twitter: {
            card: "summary_large_image",
            title: `${decodedCategory} Posts | Darshan's Blog`,
            description: `Browse all articles and tutorials about ${decodedCategory} by Darshan Bajgain`,
            creator: "@iamthesun77",
            images: ["/images/avatar.png"],
        },
    };
}

export function generateStaticParams() {
    const posts = getAllPosts();
    const categories = new Set<string>();
    posts.forEach((post) => {
        if (post.categories) {
            post.categories.forEach((category) => {
                // Ensure category is a string and trim any whitespace
                if (typeof category === 'string') {
                    categories.add(category.trim());
                }
            });
        }
    });

    // Log all categories for debugging
    console.log("All categories found:", Array.from(categories));

    // Create params with proper encoding
    const params = Array.from(categories).map((category) => {
        const encodedCategory = encodeURIComponent(category);
        console.log(`Category: "${category}" encoded as: "${encodedCategory}"`);
        return {
            category: encodedCategory,
        };
    });

    return params;
}

export default async function CategoryPage({ params }: CategoryPageProps) {
    let decodedCategory = "";
    let categoryPosts: Post[] = [];

    try {
        const { category } = await params;
        console.log("Raw category param:", category);

        // Handle double-encoded URLs (which can happen in production builds)
        let paramToUse = category;
        if (category.includes('%25')) {
            // This is likely a double-encoded URL
            paramToUse = decodeURIComponent(category);
        }

        // Ensure proper decoding
        decodedCategory = decodeURIComponent(paramToUse);
        console.log("Decoded category:", decodedCategory);

        const allPosts = getAllPosts();

        // Log all available categories for debugging
        const availableCategories = new Set<string>();
        allPosts.forEach(post => {
            if (post.categories) {
                post.categories.forEach(cat => {
                    if (typeof cat === 'string') {
                        availableCategories.add(cat.trim());
                    }
                });
            }
        });
        console.log("Available categories:", Array.from(availableCategories));

        // More flexible matching - try exact match first, then case-insensitive
        categoryPosts = allPosts.filter(
            (post) => post.categories && post.categories.some(cat =>
                cat === decodedCategory ||
                cat.toLowerCase() === decodedCategory.toLowerCase()
            )
        );

        console.log(`Found ${categoryPosts.length} posts for category "${decodedCategory}"`);

        if (categoryPosts.length === 0) {
            console.log(`No posts found for category "${decodedCategory}", trying fallback matching`);

            // Try more flexible matching as a fallback
            categoryPosts = allPosts.filter(
                (post) => post.categories && post.categories.some(cat => {
                    // Try to match with the original category name
                    if (cat === "Driving License" &&
                        (decodedCategory === "Driving License" ||
                         decodedCategory === "Driving%20License" ||
                         decodedCategory.includes("Driving"))) {
                        return true;
                    }

                    return cat.includes(decodedCategory) ||
                        decodedCategory.includes(cat) ||
                        cat.toLowerCase().includes(decodedCategory.toLowerCase()) ||
                        decodedCategory.toLowerCase().includes(cat.toLowerCase());
                })
            );

            console.log(`Found ${categoryPosts.length} posts with fallback matching`);

            if (categoryPosts.length === 0) {
                notFound();
            }
        }
    } catch (error) {
        console.error("Error in CategoryPage:", error);
        notFound();
    }

    return (
        <div className="container mb-16">
            <div className="max-w-7xl mx-auto space-y-5  px-6 md:px-12 py-5">
                <Button variant="ghost" asChild className="mb-8 pl-2">
                    <Link
                        href="/"
                        className="flex items-center gap-1 text-textPrimary/70 hover:text-textPrimary"
                    >
                        <ChevronLeft className="h-4 w-4" />
                        Go Back
                    </Link>
                </Button>
                <header className="mb-10">
                    <h1 className="text-3xl font-bold tracking-tight mb-3">
                        {decodedCategory} Posts
                    </h1>
                    <p className="text-textPrimary/70">
                        Browse all {categoryPosts.length} articles and tutorials about {decodedCategory}.
                    </p>
                </header>
                <PostList initialPosts={categoryPosts} showSearch={true} />
            </div>
        </div>
    );
}