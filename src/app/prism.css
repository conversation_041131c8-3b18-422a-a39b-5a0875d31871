/**
 * Prism.js styles for code syntax highlighting
 */

code[class*="language-"],
pre[class*="language-"] {
  color: #f8f8f2 !important;
  background: none;
  font-family: "Geist Mono", Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  text-align: left;
  white-space: pre-wrap;
  word-spacing: normal;
  word-break: break-word;
  word-wrap: break-word;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}

pre[class*="language-"] {
  padding: 1em;
  margin: .5em 0;
  overflow: auto;
  border-radius: 0.3em;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
  background: #282a36 !important;
}

:not(pre) > code[class*="language-"] {
  padding: .1em;
  border-radius: .3em;
  white-space: normal;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #6272a4;
}

.token.punctuation {
  color: #f8f8f2;
}

.namespace {
  opacity: .7;
}

.token.property,
.token.tag,
.token.constant,
.token.symbol,
.token.deleted {
  color: #ff79c6;
}

.token.boolean,
.token.number {
  color: #bd93f9;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #50fa7b;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
  color: #f8f8f2;
}

.token.atrule,
.token.attr-value,
.token.keyword {
  color: #ff79c6;
}

.token.function,
.token.class-name {
  color: #8be9fd;
}

.token.regex,
.token.important,
.token.variable {
  color: #f1fa8c;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.entity {
  cursor: help;
}

/* Safari-specific fixes */
@supports (-webkit-touch-callout: none) {
  .token.comment,
  .token.prolog,
  .token.doctype,
  .token.cdata {
    color: #6272a4 !important;
  }

  .token.punctuation {
    color: #f8f8f2 !important;
  }

  .token.property,
  .token.tag,
  .token.constant,
  .token.symbol,
  .token.deleted {
    color: #ff79c6 !important;
  }

  .token.boolean,
  .token.number {
    color: #bd93f9 !important;
  }

  .token.selector,
  .token.attr-name,
  .token.string,
  .token.char,
  .token.builtin,
  .token.inserted {
    color: #50fa7b !important;
  }

  .token.operator,
  .token.entity,
  .token.url,
  .language-css .token.string,
  .style .token.string {
    color: #f8f8f2 !important;
  }

  .token.atrule,
  .token.attr-value,
  .token.keyword {
    color: #ff79c6 !important;
  }

  .token.function,
  .token.class-name {
    color: #8be9fd !important;
  }

  .token.regex,
  .token.important,
  .token.variable {
    color: #f1fa8c !important;
  }
}

/* Line highlighting */
pre[data-line] {
  position: relative;
  padding: 1em 0 1em 3em;
}

.line-highlight {
  position: absolute;
  left: 0;
  right: 0;
  padding: inherit 0;
  margin-top: 1em;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: inset 5px 0 0 #f1fa8c;
  z-index: 0;
  pointer-events: none;
  line-height: inherit;
  white-space: pre;
}
