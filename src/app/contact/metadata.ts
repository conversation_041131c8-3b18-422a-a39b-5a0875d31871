import type { Metadata } from "next";

export const metadata: Metadata = {
    title: "Contact Me | Darshan's Blog",
    description: "Get in touch with <PERSON><PERSON> Bajgain for collaborations, questions, or just to say hello",
    keywords: ["contact", "Darshan Bajgain", "frontend developer", "collaboration", "hire"],
    openGraph: {
        title: "Contact Me | Darshan's Blog",
        description: "Get in touch with Darshan Bajgain for collaborations, questions, or just to say hello",
        url: "https://blog.darshanbajgain.com.np/contact",
        type: "website",
        images: [
            {
                url: "/images/avatar.png",
                width: 1200,
                height: 630,
                alt: "Contact Darshan Bajgain"
            }
        ],
    },
    twitter: {
        card: "summary_large_image",
        title: "Contact Me | Darshan's Blog",
        description: "Get in touch with Darshan Bajgain for collaborations, questions, or just to say hello",
        images: ["/images/avatar.png"],
    },
};
