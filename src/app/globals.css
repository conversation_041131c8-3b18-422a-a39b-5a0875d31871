@import "tailwindcss";

@import "tw-animate-css";
@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --color-accent: var(--accent);
  --color-accent-hover: var(--accent-hover);
  --color-textSecondary: var(--text-secondary);
  --color-textPrimary: var(--text-primary);
  --color-textMuted: var(--text-muted);
  --color-backgroundSecondary: var(--background-secondary);
  --color-backgroundPrimary: var(--background-primary);
  --color-borderColor: var(--border-color);
  --color-borderHover: var(--border-hover);
  --color-cardBackground: var(--card-background);
  --color-cardHover: var(--card-hover);
  --color-codeBackground: var(--code-background);

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

:root {
  /* Clean, minimal light mode palette */
  --background-primary: #ffffff; /* Pure white */
  --background-secondary: #fafafa; /* Subtle gray background */
  --text-primary: #1a1a1a; /* Near black for excellent readability */
  --text-secondary: #666666; /* Medium gray for secondary text */
  --text-muted: #999999; /* Light gray for muted text */
  --accent: #0070f3; /* Clean blue accent */
  --accent-hover: #0051cc; /* Darker blue for hover states */
  --border-color: #e5e5e5; /* Light gray border */
  --border-hover: #d4d4d4; /* Slightly darker border for hover */
  --card-background: #ffffff; /* White cards */
  --card-hover: #fafafa; /* Subtle hover state for cards */
  --code-background: #f5f5f5; /* Light gray for code blocks */
}

.dark {
  /* Clean, minimal dark mode palette */
  --background-primary: #0a0a0a; /* Deep black background */
  --background-secondary: #111111; /* Slightly lighter black */
  --text-primary: #ffffff; /* Pure white text */
  --text-secondary: #a3a3a3; /* Light gray for secondary text */
  --text-muted: #737373; /* Medium gray for muted text */
  --accent: #3b82f6; /* Bright blue accent */
  --accent-hover: #2563eb; /* Darker blue for hover */
  --border-color: #262626; /* Dark gray border */
  --border-hover: #404040; /* Lighter gray for hover */
  --card-background: #111111; /* Dark cards */
  --card-hover: #1a1a1a; /* Subtle hover state */
  --code-background: #1a1a1a; /* Dark gray for code blocks */
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Theme color schemes */
html {
  color-scheme: light;
}

html.dark {
  color-scheme: dark;
}

/* Grid pattern for hero section */
.bg-grid-pattern {
  background-image: linear-gradient(to right, currentColor 1px, transparent 1px),
    linear-gradient(to bottom, currentColor 1px, transparent 1px);
  background-size: 40px 40px;
}

/* Code block styling */
.code-block-wrapper {
  margin: 1.5rem 0;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 1px solid rgba(226, 232, 240, 0.2);
  background-color: #1e293b;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.code-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: rgba(15, 23, 42, 0.8);
  border-bottom: 1px solid rgba(226, 232, 240, 0.1);
}

.code-language {
  font-family: "Geist Mono", monospace;
  font-size: 0.75rem;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.copy-code-button {
  background-color: rgba(226, 232, 240, 0.1);
  border: none;
  border-radius: 0.25rem;
  color: #94a3b8;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-code-button:hover {
  background-color: rgba(226, 232, 240, 0.2);
  color: #f8fafc;
}

.copy-code-button:focus {
  outline: 2px solid rgba(56, 189, 248, 0.5);
  outline-offset: 2px;
}

.code-block-content {
  padding: 0;
  margin: 0;
  overflow-x: auto;
}

.code-block-content pre {
  margin: 0;
  padding: 1rem;
  background-color: transparent;
  overflow-x: auto;
}

.code-block-content code {
  font-family: "Geist Mono", monospace;
  font-size: 0.875rem;
  line-height: 1.7;
  white-space: pre;
  word-break: normal;
  tab-size: 2;
}

/* Dark mode adjustments */
.dark .code-block-wrapper {
  border-color: rgba(51, 65, 85, 0.5);
}

.dark .code-block-header {
  background-color: rgba(15, 23, 42, 1);
}

/* Simple inline code styling - Higher specificity to override Tailwind prose */
.prose code:not([class*="language-"]) {
  background-color: rgba(226, 232, 240, 0.3) !important;
  padding: 0.2rem 0.4rem !important;
  border-radius: 0.25rem !important;
  font-family: "Geist Mono", monospace !important;
  font-size: 0.875rem !important;
  word-break: break-word !important;
  color: #1e293b !important; /* Explicit color for Safari compatibility */
}

/* Dark mode inline code styling - Higher specificity */
.dark .prose code:not([class*="language-"]) {
  background-color: rgba(51, 65, 85, 0.4) !important;
  color: #e2e8f0 !important; /* Light color for dark mode */
  border: 1px solid rgba(51, 65, 85, 0.6) !important;
}

/* Enhanced styling for CSS properties and functions in inline code */
.prose code:not([class*="language-"]):is([data-css-property], [title*="@"], [title*="()"], [title*="color-mix"], [title*="@property"]) {
  background-color: rgba(99, 102, 241, 0.1) !important;
  color: #4f46e5 !important;
  font-weight: 500 !important;
}

.dark .prose code:not([class*="language-"]):is([data-css-property], [title*="@"], [title*="()"], [title*="color-mix"], [title*="@property"]) {
  background-color: rgba(99, 102, 241, 0.2) !important;
  color: #a5b4fc !important;
  border-color: rgba(99, 102, 241, 0.3) !important;
}

/* Utility classes for better inline code styling */
.prose .css-property {
  background-color: rgba(99, 102, 241, 0.1) !important;
  color: #4f46e5 !important;
  font-weight: 500 !important;
  padding: 0.2rem 0.4rem !important;
  border-radius: 0.25rem !important;
  font-family: "Geist Mono", monospace !important;
  font-size: 0.875rem !important;
}

.dark .prose .css-property {
  background-color: rgba(99, 102, 241, 0.2) !important;
  color: #a5b4fc !important;
  border: 1px solid rgba(99, 102, 241, 0.3) !important;
}

.prose .css-function {
  background-color: rgba(16, 185, 129, 0.1) !important;
  color: #059669 !important;
  font-weight: 500 !important;
  padding: 0.2rem 0.4rem !important;
  border-radius: 0.25rem !important;
  font-family: "Geist Mono", monospace !important;
  font-size: 0.875rem !important;
}

.dark .prose .css-function {
  background-color: rgba(16, 185, 129, 0.2) !important;
  color: #6ee7b7 !important;
  border: 1px solid rgba(16, 185, 129, 0.3) !important;
}

/* Safari-specific fixes */
@supports (-webkit-touch-callout: none) {
  .prose code:not([class*="language-"]) {
    color: #1e293b !important;
  }

  .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    color: #1e293b !important;
  }

  .prose p, .prose li, .prose blockquote {
    color: #1e293b !important;
  }

  .prose a {
    color: #2563eb !important;
  }

  .prose a:hover {
    color: #1d4ed8 !important;
  }

  /* Safari dark mode fixes */
  .dark .prose code:not([class*="language-"]) {
    color: #e2e8f0 !important;
    background-color: rgba(51, 65, 85, 0.4) !important;
  }

  .dark .prose h1, .dark .prose h2, .dark .prose h3, .dark .prose h4, .dark .prose h5, .dark .prose h6 {
    color: #f8fafc !important;
  }

  .dark .prose p, .dark .prose li, .dark .prose blockquote {
    color: #f8fafc !important;
  }

  .dark .prose a {
    color: #38bdf8 !important;
  }

  .dark .prose a:hover {
    color: #0ea5e9 !important;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .code-block-content {
    max-width: 100%;
    overflow-x: auto;
  }

  .code-block-content code {
    font-size: 0.75rem;
  }
}

/* Make links break properly on small screens */
.prose a {
  word-break: break-word;
  overflow-wrap: break-word;
  display: inline-block;
  max-width: 100%;
}

/* Language-specific syntax coloring */
.prose .language-javascript .keyword,
.prose .language-typescript .keyword,
.prose .language-jsx .keyword,
.prose .language-tsx .keyword,
.prose .language-css .keyword,
.prose .language-html .keyword {
  color: #ff79c6;
}

.prose .language-javascript .function,
.prose .language-typescript .function,
.prose .language-jsx .function,
.prose .language-tsx .function {
  color: #50fa7b;
}

.prose .language-javascript .string,
.prose .language-typescript .string,
.prose .language-jsx .string,
.prose .language-tsx .string,
.prose .language-css .string,
.prose .language-html .string {
  color: #f1fa8c;
}

.prose .language-javascript .number,
.prose .language-typescript .number,
.prose .language-jsx .number,
.prose .language-tsx .number {
  color: #bd93f9;
}

.prose .language-javascript .comment,
.prose .language-typescript .comment,
.prose .language-jsx .comment,
.prose .language-tsx .comment,
.prose .language-css .comment,
.prose .language-html .comment {
  color: #6272a4;
  font-style: italic;
}

.prose .language-javascript .operator,
.prose .language-typescript .operator,
.prose .language-jsx .operator,
.prose .language-tsx .operator {
  color: #ff79c6;
}

.prose .language-bash .function,
.prose .language-shell .function {
  color: #50fa7b;
}

.prose .language-bash .string,
.prose .language-shell .string {
  color: #f1fa8c;
}

.prose .language-json .property {
  color: #ff79c6;
}

.prose .language-json .string {
  color: #f1fa8c;
}

.prose .language-json .number {
  color: #bd93f9;
}

/* CSS specific */
.prose .language-css .property {
  color: #ff79c6;
}

.prose .language-css .value {
  color: #f1fa8c;
}

/* HTML specific */
.prose .language-html .tag {
  color: #ff79c6;
}

.prose .language-html .attr-name {
  color: #50fa7b;
}

.prose .language-html .attr-value {
  color: #f1fa8c;
}

/* Improve image display in blog posts */
.prose img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1.5rem auto;
  border: 1px solid var(--border-color);
  display: block;
  object-fit: contain;
}

/* Center images and limit their width on larger screens */
@media (min-width: 768px) {
  .prose img {
    max-width: min(100%, 700px); /* As per user requirement */
  }
}

/* Responsive typography for better readability on different devices */
@media (max-width: 640px) {
  .prose h1 {
    font-size: 1.75rem;
    line-height: 1.3;
  }

  .prose h2 {
    font-size: 1.5rem;
    line-height: 1.3;
  }

  .prose h3 {
    font-size: 1.25rem;
    line-height: 1.3;
  }

  .prose p,
  .prose li {
    font-size: 1rem;
    line-height: 1.6;
  }

  .prose blockquote {
    padding-left: 1rem;
    margin-left: 0;
    margin-right: 0;
  }
}

/* Improve typography for code blocks */
.prose p,
.prose li,
.prose blockquote {
  line-height: 1.75;
}

/* Improve ordered and unordered lists */
.prose ol,
.prose ul {
  margin-top: 1rem;
  margin-bottom: 1rem;
  padding-left: 2rem;
}

/* Table styling */
.prose table {
  width: 100%;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  border-collapse: collapse;
  overflow-x: auto;
  display: block;
}

.prose table thead {
  background-color: rgba(226, 232, 240, 0.2);
  border-bottom: 2px solid rgba(226, 232, 240, 0.5);
}

.prose table th {
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
}

.prose table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.2);
  vertical-align: top;
}

.prose table tr:last-child td {
  border-bottom: none;
}

.prose table tr:nth-child(even) {
  background-color: rgba(226, 232, 240, 0.1);
}

@media (min-width: 768px) {
  .prose table {
    display: table;
    width: 100%;
  }
}

.prose ol {
  list-style-type: decimal;
  list-style-position: outside;
}

.prose ul {
  list-style-type: none;
  list-style-position: outside;
  padding-left: 1.5rem;
}

.prose ul li {
  position: relative;
  line-height: 1.75;
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose ul li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0.5rem;
  height: 0.5rem;
  width: 0.5rem;
  background-color: var(--buttons);
  border-radius: 50%;
  transform: scale(1);
  transition: transform 0.2s ease;
}

.prose ul li:hover::before {
  transform: scale(1.3);
}

/* Special styling for feature bullet points */
.prose ul.feature-list {
  margin-left: 0;
  padding-left: 0;
}

.prose ul.feature-list li {
  position: relative;
  padding-left: 2.5rem;
  margin-bottom: 1.25rem;
  min-height: 2rem;
}

.prose ul.feature-list li::before {
  content: "✓";
  font-size: 1rem;
  color: white;
  background-color: var(--buttons);
  height: 1.5rem;
  width: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  left: 0;
  top: 0.125rem;
  transform: scale(1);
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.prose ul.feature-list li:hover::before {
  transform: scale(1.1);
  background-color: var(--buttons);
  opacity: 0.8;
}

.prose ol li {
  line-height: 1.75;
  margin-bottom: 0.5rem;
  padding-left: 0.5rem;
}

/* Fix nested lists */
.prose ol ol,
.prose ul ul,
.prose ol ul,
.prose ul ol {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.prose ol ol {
  list-style-type: lower-alpha;
}

.prose ol ol ol {
  list-style-type: lower-roman;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-weight: 700;
  line-height: 1.3;
}

.prose h2 {
  font-size: 1.75rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  padding-bottom: 0.5rem;
}

.prose h3 {
  font-size: 1.5rem;
}

/* Animation for blog cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.5s ease-out forwards;
}
