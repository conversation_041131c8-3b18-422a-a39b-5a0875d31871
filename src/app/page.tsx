import { getAllPosts } from "@/lib/post"
import LatestPosts from "@/components/blog/LatestPost"
import FeaturedPosts from "@/components/blog/FeaturedPost"
import TopCategories from "@/components/blog/TopCategories"
import Newsletter from "@/components/blog/Newsletter"
import Hero from "@/components/blog/Hero"
import type { Metadata } from "next"
import Script from "next/script"

export const metadata: Metadata = {
  title: "Home | Darshan's Blog",
  description: "A personal blog sharing frontend development insights and daily life experiences by <PERSON><PERSON>gai<PERSON>",
  keywords: ["frontend development", "web development", "React", "Next.js", "JavaScript", "TypeScript", "personal blog", "Darshan Bajgain"],
  openGraph: {
    title: "<PERSON><PERSON>'s Blog",
    description: "A personal blog sharing frontend development insights and daily life experiences by <PERSON><PERSON>gai<PERSON>",
    url: "https://blog.darshanbajgain.com.np",
    type: "website",
    images: [
      {
        url: "/images/avatar.png",
        width: 1200,
        height: 630,
        alt: "<PERSON><PERSON>'s Blog"
      }
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "<PERSON><PERSON>'s Blog",
    description: "A personal blog sharing frontend development insights and daily life experiences by <PERSON><PERSON>gain",
    creator: "@iamthesun77",
    images: ["/images/avatar.png"],
  },
}

export default function Home() {
  const posts = getAllPosts()

  // Get the latest 6 posts for the homepage
  const latestPosts = posts.slice(0, 6)

  // Create structured data for the blog homepage
  const siteUrl = "https://blog.darshanbajgain.com.np"
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Blog",
    "name": "Darshan's Blog",
    "description": "A personal blog covering frontend development and personal daily blogs",
    "url": siteUrl,
    "author": {
      "@type": "Person",
      "name": "Darshan Bajgain",
      "url": "https://darshanbajgain.com.np"
    },
    "publisher": {
      "@type": "Person",
      "name": "Darshan Bajgain",
      "url": "https://darshanbajgain.com.np"
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": siteUrl
    },
    "blogPost": posts.slice(0, 10).map(post => ({
      "@type": "BlogPosting",
      "headline": post.title,
      "description": post.description,
      "datePublished": new Date(post.date).toISOString(),
      "url": `${siteUrl}/posts/${post.slug}`
    }))
  }

  // Count posts by category
  const categoryCount = posts.reduce(
    (acc, post) => {
      if (post.categories) {
        post.categories.forEach((cat) => {
          acc[cat] = (acc[cat] || 0) + 1
        })
      }
      return acc
    },
    {} as Record<string, number>,
  )

  // Get top 5 categories
  const topCategories = Object.entries(categoryCount)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)

  // Get featured posts - you can customize this selection
  // Option 1: Use the latest posts
  // const featuredPost = posts.slice(0, 2)

  // Option 2: Select specific posts by slug
  // const featuredPosts = [
  //   posts.find(post => post.slug === 'getting-started-with-nextjs'),
  //   posts.find(post => post.slug === 'another-post-slug'),
  //   posts.find(post => post.slug === 'third-post-slug'),
  // ].filter(Boolean) as Post[]

  // Option 3: Select posts by category
  const featuredPost = posts.filter(post =>
    post.categories?.includes('ConvertKit') || post.categories?.includes('Next.js') ||post.categories?.includes('Nepal')
  ).slice(0, 3)

  //  Select specific posts by slug
  // const featuredPost = [
  //   posts.find(post => post.slug === 'getting-started-with-nextjs'),
  //   posts.find(post => post.slug === 'setting-up-convertkit-for-email-subscriptions'),
  // ].filter(Boolean) as Post[]

  return (
    <>
      {/* Add structured data for SEO */}
      <Script id="blog-schema" type="application/ld+json">
        {JSON.stringify(jsonLd)}
      </Script>
      <div className="flex flex-col gap-16 pb-16 mb-8">
      {/* Hero Section with Stats */}
      <Hero />

      {/* Featured Post */}
      <FeaturedPosts posts={featuredPost} title="Featured Posts" maxPosts={3} />


      {/* Top Categories */}
      <TopCategories
        categories={topCategories}
        title="Popular Categories"
        description="Browse posts by your favorite topics"
        maxCategories={5}
      />

      {/* Latest Posts */}
      <LatestPosts posts={latestPosts} totalPosts={posts.length} />

      {/* Newsletter */}
      <Newsletter />
    </div>
    </>
  )
}